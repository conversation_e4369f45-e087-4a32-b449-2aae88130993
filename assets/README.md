# Assets Directory

This directory contains placeholder assets for the Bible Companion app.

## Required Assets

The following assets are referenced in the app configuration but need to be created:

- `icon.png` - App icon (1024x1024px)
- `splash.png` - Splash screen image
- `adaptive-icon.png` - Android adaptive icon foreground (1024x1024px)
- `favicon.png` - Web favicon (32x32px or 16x16px)

## Asset Guidelines

### App Icon (`icon.png`)

- Size: 1024x1024 pixels
- Format: PNG with transparency
- Should represent the Bible Companion brand
- Will be automatically resized for different platforms

### Splash Screen (`splash.png`)

- Recommended size: 1242x2436 pixels (iPhone X resolution)
- Format: PNG
- Should match the app's branding
- Background color is set to white in app.json

### Adaptive Icon (`adaptive-icon.png`)

- Size: 1024x1024 pixels
- Format: PNG with transparency
- Android adaptive icon foreground layer
- Should work well with various background shapes

### Favicon (`favicon.png`)

- Size: 32x32 or 16x16 pixels
- Format: PNG
- Used for web version of the app

## Placeholder Status

Currently, these are placeholder files that need to be replaced with actual designed assets.
