# Dependencies
node_modules
.pnp
.pnp.js
yarn.lock
package-lock.json

# Build outputs
dist
build
out
lib
coverage

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment and config
.env
.env.*
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache and temporary files
.cache
.tmp
.temp
.eslintcache

# Generated files
*.generated.*
*.min.*

# System files
.DS_Store
Thumbs.db

# IDE specific files
.idea
.vscode
*.swp
*.swo
