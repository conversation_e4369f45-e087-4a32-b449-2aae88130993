{"compilerOptions": {"strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "skipLibCheck": true, "jsx": "react-jsx", "target": "ES2022", "lib": ["ES2022", "DOM"], "types": ["react", "react-native"], "allowJs": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/screens/*": ["./src/screens/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/theme/*": ["./src/theme/*"]}}, "include": ["app/**/*.ts", "app/**/*.tsx", "src/**/*.ts", "src/**/*.tsx", "src/types/global.d.ts", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"], "extends": "expo/tsconfig.base"}