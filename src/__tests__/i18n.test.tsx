import i18n from '../i18n';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
}));

// Mock expo-localization
jest.mock('expo-localization', () => ({
  locale: 'en-US',
}));

// Wait for i18n to be ready
const waitForI18n = () => new Promise(resolve => {
  if (i18n.isInitialized) {
    resolve(true);
  } else {
    i18n.on('initialized', () => resolve(true));
  }
});

describe('i18n', () => {
  beforeEach(async () => {
    await waitForI18n();
    // Reset i18n to English before each test
    await i18n.changeLanguage('en');
  });

  it('should have English translations by default', async () => {
    await waitForI18n();

    expect(i18n.t('welcome')).toBe('Welcome to Bible Companion');
    expect(i18n.t('login')).toBe('Login');
    expect(i18n.t('dailyVerse')).toBe('Daily Verse');
    expect(i18n.t('share')).toBe('Share');
  });

  it('should switch to French translations', async () => {
    await waitForI18n();

    // Change language to French
    await i18n.changeLanguage('fr');

    expect(i18n.t('welcome')).toBe('Bienvenue dans Bible Companion');
    expect(i18n.t('login')).toBe('Connexion');
    expect(i18n.t('dailyVerse')).toBe('Verset du Jour');
    expect(i18n.t('share')).toBe('Partager');
  });

  it('should switch to Kinyarwanda translations', async () => {
    await waitForI18n();

    // Change language to Kinyarwanda
    await i18n.changeLanguage('rw');

    expect(i18n.t('welcome')).toBe('Murakaza neza kuri Bible Companion');
    expect(i18n.t('login')).toBe('Kwinjira');
    expect(i18n.t('dailyVerse')).toBe('Ijambo ry\'Umunsi');
    expect(i18n.t('share')).toBe('Gusangira');
  });

  it('should switch to Swahili translations', async () => {
    await waitForI18n();

    // Change language to Swahili
    await i18n.changeLanguage('sw');

    expect(i18n.t('welcome')).toBe('Karibu kwenye Bible Companion');
    expect(i18n.t('login')).toBe('Ingia');
    expect(i18n.t('dailyVerse')).toBe('Aya ya Kila Siku');
    expect(i18n.t('share')).toBe('Shiriki');
  });

  it('should fall back to English for unsupported languages', async () => {
    await waitForI18n();

    // Try to change to unsupported language
    await i18n.changeLanguage('de'); // German - not supported

    // Should fall back to English
    expect(i18n.t('welcome')).toBe('Welcome to Bible Companion');
    expect(i18n.t('login')).toBe('Login');
    expect(i18n.t('dailyVerse')).toBe('Daily Verse');
    expect(i18n.t('share')).toBe('Share');
  });

  it('should have all required translation keys', () => {
    const requiredKeys = ['welcome', 'login', 'dailyVerse', 'share'];
    const languages = ['en', 'rw', 'sw', 'fr'];

    languages.forEach(lang => {
      requiredKeys.forEach(key => {
        const translation = i18n.getResource(lang, 'translation', key);
        expect(translation).toBeDefined();
        expect(typeof translation).toBe('string');
        expect(translation.length).toBeGreaterThan(0);
      });
    });
  });
});
