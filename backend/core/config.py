"""
Configuration settings for Bible Companion API.
"""

import os
from functools import lru_cache
from typing import List

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    DEBUG: bool = Field(default=False, description="Debug mode")
    SECRET_KEY: str = Field(..., description="Secret key for JWT")
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8081"],
        description="Allowed CORS origins"
    )
    
    # Database
    MONGODB_URI: str = Field(..., description="MongoDB connection string")
    MONGODB_DB_NAME: str = Field(default="bible_companion", description="MongoDB database name")
    
    NEO4J_URI: str = Field(..., description="Neo4j connection URI")
    NEO4J_USERNAME: str = Field(..., description="Neo4j username")
    NEO4J_PASSWORD: str = Field(..., description="Neo4j password")
    
    # TGI (Text Generation Inference)
    TGI_BASE_URL: str = Field(
        default="http://localhost:8080",
        description="TGI server base URL"
    )
    TGI_TIMEOUT: int = Field(default=30, description="TGI request timeout in seconds")
    TGI_MAX_RETRIES: int = Field(default=3, description="TGI max retry attempts")
    
    # Firebase
    FIREBASE_PROJECT_ID: str = Field(..., description="Firebase project ID")
    FIREBASE_CREDENTIALS_PATH: str = Field(
        default="",
        description="Path to Firebase service account credentials JSON"
    )
    
    # Chat Configuration
    CHAT_MAX_HISTORY: int = Field(default=10, description="Maximum chat history to keep")
    CHAT_CONTEXT_VERSES: int = Field(default=20, description="Number of recent verses for context")
    CHAT_MAX_TOKENS: int = Field(default=512, description="Maximum tokens for chat response")
    CHAT_TEMPERATURE: float = Field(default=0.7, description="Chat response temperature")
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, description="Rate limit per minute per user")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
