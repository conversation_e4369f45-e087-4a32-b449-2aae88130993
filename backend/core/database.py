"""
Database connections and utilities for Bible Companion API.
"""

import asyncio
from typing import Any, Dict, List, Optional

import structlog
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from neo4j import AsyncGraphDatabase, AsyncDriver
from pymongo.errors import ConnectionFailure

from backend.core.config import get_settings
from backend.core.exceptions import DatabaseError

logger = structlog.get_logger()


class MongoDBClient:
    """MongoDB async client wrapper."""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self.settings = get_settings()
    
    async def connect(self) -> None:
        """Connect to MongoDB."""
        try:
            self.client = AsyncIOMotorClient(self.settings.MONGODB_URI)
            self.database = self.client[self.settings.MONGODB_DB_NAME]
            
            # Test connection
            await self.client.admin.command('ping')
            logger.info("MongoDB connection established")
            
        except ConnectionFailure as e:
            logger.error("Failed to connect to MongoDB", error=str(e))
            raise DatabaseError(f"MongoDB connection failed: {str(e)}")
    
    async def close(self) -> None:
        """Close MongoDB connection."""
        if self.client:
            self.client.close()
            logger.info("MongoDB connection closed")
    
    def get_collection(self, name: str):
        """Get collection by name."""
        if not self.database:
            raise DatabaseError("Database not connected")
        return self.database[name]
    
    async def health_check(self) -> bool:
        """Check MongoDB health."""
        try:
            if not self.client:
                return False
            await self.client.admin.command('ping')
            return True
        except Exception:
            return False


class Neo4jClient:
    """Neo4j async client wrapper."""
    
    def __init__(self):
        self.driver: Optional[AsyncDriver] = None
        self.settings = get_settings()
    
    async def connect(self) -> None:
        """Connect to Neo4j."""
        try:
            self.driver = AsyncGraphDatabase.driver(
                self.settings.NEO4J_URI,
                auth=(self.settings.NEO4J_USERNAME, self.settings.NEO4J_PASSWORD)
            )
            logger.info("Neo4j connection established")
            
        except Exception as e:
            logger.error("Failed to connect to Neo4j", error=str(e))
            raise DatabaseError(f"Neo4j connection failed: {str(e)}")
    
    async def close(self) -> None:
        """Close Neo4j connection."""
        if self.driver:
            await self.driver.close()
            logger.info("Neo4j connection closed")
    
    async def verify_connectivity(self) -> None:
        """Verify Neo4j connectivity."""
        if not self.driver:
            await self.connect()
        
        try:
            await self.driver.verify_connectivity()
        except Exception as e:
            raise DatabaseError(f"Neo4j connectivity check failed: {str(e)}")
    
    async def execute_query(
        self,
        query: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """Execute a Neo4j query."""
        if not self.driver:
            raise DatabaseError("Neo4j driver not connected")
        
        async with self.driver.session() as session:
            try:
                result = await session.run(query, parameters or {})
                records = await result.data()
                return records
            except Exception as e:
                logger.error("Neo4j query failed", query=query, error=str(e))
                raise DatabaseError(f"Neo4j query failed: {str(e)}")
    
    async def health_check(self) -> bool:
        """Check Neo4j health."""
        try:
            if not self.driver:
                return False
            await self.driver.verify_connectivity()
            return True
        except Exception:
            return False


# Global instances
mongodb_client = MongoDBClient()
neo4j_driver = Neo4jClient()
