"""
Custom exceptions for Bible Companion API.
"""

from typing import Any, Dict, Optional


class BibleCompanionException(Exception):
    """Base exception for Bible Companion API."""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)


class AuthenticationError(BibleCompanionException):
    """Authentication related errors."""
    
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, status_code=401)


class AuthorizationError(BibleCompanionException):
    """Authorization related errors."""
    
    def __init__(self, message: str = "Access denied"):
        super().__init__(message, status_code=403)


class ValidationError(BibleCompanionException):
    """Request validation errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=400, details=details)


class NotFoundError(BibleCompanionException):
    """Resource not found errors."""
    
    def __init__(self, message: str = "Resource not found"):
        super().__init__(message, status_code=404)


class ConflictError(BibleCompanionException):
    """Resource conflict errors."""
    
    def __init__(self, message: str = "Resource conflict"):
        super().__init__(message, status_code=409)


class TGIError(BibleCompanionException):
    """TGI service related errors."""
    
    def __init__(self, message: str = "Text generation service error"):
        super().__init__(message, status_code=502)


class DatabaseError(BibleCompanionException):
    """Database related errors."""
    
    def __init__(self, message: str = "Database operation failed"):
        super().__init__(message, status_code=500)


class RateLimitError(BibleCompanionException):
    """Rate limiting errors."""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, status_code=429)
