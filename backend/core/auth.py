"""
Authentication utilities for Bible Companion API.
"""

import json
from typing import Optional

import firebase_admin
import structlog
from fastapi import Depends, HTTPException, Request
from fastapi.security import HTTPAuthorizationCredentials, HTTP<PERSON>earer
from firebase_admin import auth, credentials

from backend.core.config import get_settings
from backend.core.exceptions import AuthenticationError, AuthorizationError

logger = structlog.get_logger()

# Initialize Firebase Admin SDK
settings = get_settings()

try:
    if settings.FIREBASE_CREDENTIALS_PATH:
        # Use service account file
        cred = credentials.Certificate(settings.FIREBASE_CREDENTIALS_PATH)
    else:
        # Use default credentials (for Cloud Run, etc.)
        cred = credentials.ApplicationDefault()
    
    firebase_admin.initialize_app(cred, {
        'projectId': settings.FIREBASE_PROJECT_ID,
    })
    logger.info("Firebase Admin SDK initialized")
    
except Exception as e:
    logger.error("Failed to initialize Firebase Admin SDK", error=str(e))
    raise


class User:
    """Authenticated user model."""
    
    def __init__(self, uid: str, email: Optional[str] = None, **kwargs):
        self.uid = uid
        self.email = email
        self.custom_claims = kwargs.get('custom_claims', {})
        self.email_verified = kwargs.get('email_verified', False)
        self.phone_number = kwargs.get('phone_number')
        self.provider_data = kwargs.get('provider_data', [])


security = HTTPBearer()


async def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Extract and validate Firebase JWT token from request.
    
    Args:
        request: FastAPI request object
        credentials: HTTP Bearer token
        
    Returns:
        User: Authenticated user object
        
    Raises:
        AuthenticationError: If token is invalid or expired
    """
    token = credentials.credentials
    
    try:
        # Verify the Firebase ID token
        decoded_token = auth.verify_id_token(token)
        
        # Extract user information
        user = User(
            uid=decoded_token['uid'],
            email=decoded_token.get('email'),
            email_verified=decoded_token.get('email_verified', False),
            phone_number=decoded_token.get('phone_number'),
            custom_claims=decoded_token.get('custom_claims', {}),
            provider_data=decoded_token.get('firebase', {}).get('identities', {})
        )
        
        logger.info("User authenticated", user_id=user.uid, email=user.email)
        return user
        
    except auth.InvalidIdTokenError:
        logger.warning("Invalid Firebase ID token", token_prefix=token[:20])
        raise AuthenticationError("Invalid authentication token")
    
    except auth.ExpiredIdTokenError:
        logger.warning("Expired Firebase ID token", token_prefix=token[:20])
        raise AuthenticationError("Authentication token has expired")
    
    except Exception as e:
        logger.error("Authentication error", error=str(e))
        raise AuthenticationError("Authentication failed")


async def get_optional_user(
    request: Request,
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[User]:
    """
    Extract user from request if token is provided, otherwise return None.
    
    Args:
        request: FastAPI request object
        credentials: Optional HTTP Bearer token
        
    Returns:
        Optional[User]: Authenticated user or None
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(request, credentials)
    except AuthenticationError:
        return None


def require_admin(user: User = Depends(get_current_user)) -> User:
    """
    Require admin privileges.
    
    Args:
        user: Authenticated user
        
    Returns:
        User: Admin user
        
    Raises:
        AuthorizationError: If user is not admin
    """
    if not user.custom_claims.get('admin', False):
        logger.warning("Admin access denied", user_id=user.uid)
        raise AuthorizationError("Admin privileges required")
    
    return user


def require_verified_email(user: User = Depends(get_current_user)) -> User:
    """
    Require verified email address.
    
    Args:
        user: Authenticated user
        
    Returns:
        User: User with verified email
        
    Raises:
        AuthorizationError: If email is not verified
    """
    if not user.email_verified:
        logger.warning("Email verification required", user_id=user.uid)
        raise AuthorizationError("Email verification required")
    
    return user
