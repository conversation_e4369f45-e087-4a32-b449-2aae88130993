"""
TGI (Text Generation Inference) client for Bible Companion API.
"""

import async<PERSON>
from typing import Any, Dict, Optional

import httpx
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential

from backend.core.config import get_settings
from backend.core.exceptions import TGIError
from backend.models.chat import TGIRequest, TGIResponse

logger = structlog.get_logger()


class TGIClient:
    """Client for Text Generation Inference server."""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = self.settings.TGI_BASE_URL
        self.timeout = self.settings.TGI_TIMEOUT
        self.max_retries = self.settings.TGI_MAX_RETRIES
        
        # HTTP client configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
        )
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def close(self):
        """Close HTTP client."""
        await self.client.aclose()
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def health_check(self) -> bool:
        """Check TGI server health."""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            return response.status_code == 200
        except Exception as e:
            logger.warning("TGI health check failed", error=str(e))
            return False
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def generate_text(
        self,
        prompt: str,
        max_new_tokens: int = 512,
        temperature: float = 0.7,
        top_p: float = 0.9,
        do_sample: bool = True,
        stop_sequences: Optional[list] = None,
        **kwargs
    ) -> str:
        """
        Generate text using TGI server.
        
        Args:
            prompt: Input prompt for text generation
            max_new_tokens: Maximum number of new tokens to generate
            temperature: Sampling temperature (0.0 to 2.0)
            top_p: Top-p sampling parameter
            do_sample: Whether to use sampling
            stop_sequences: List of stop sequences
            **kwargs: Additional parameters
            
        Returns:
            str: Generated text
            
        Raises:
            TGIError: If generation fails
        """
        try:
            # Prepare request payload
            request_data = TGIRequest(
                inputs=prompt,
                parameters={
                    "max_new_tokens": max_new_tokens,
                    "temperature": temperature,
                    "top_p": top_p,
                    "do_sample": do_sample,
                    "return_full_text": False,
                    "stop": stop_sequences or [],
                    **kwargs
                },
                stream=False
            )
            
            logger.info(
                "Sending TGI request",
                prompt_length=len(prompt),
                max_tokens=max_new_tokens,
                temperature=temperature
            )
            
            # Make request to TGI server
            response = await self.client.post(
                f"{self.base_url}/generate",
                json=request_data.model_dump(),
                headers={"Content-Type": "application/json"}
            )
            
            # Check response status
            if response.status_code != 200:
                error_msg = f"TGI request failed with status {response.status_code}"
                try:
                    error_detail = await response.json()
                    error_msg += f": {error_detail}"
                except:
                    error_msg += f": {response.text}"
                
                logger.error("TGI request failed", status_code=response.status_code, error=error_msg)
                raise TGIError(error_msg)
            
            # Parse response
            response_data = await response.json()
            
            # Handle different response formats
            if isinstance(response_data, list) and len(response_data) > 0:
                # Multiple generations (take first)
                generated_text = response_data[0].get("generated_text", "")
            elif isinstance(response_data, dict):
                # Single generation
                generated_text = response_data.get("generated_text", "")
            else:
                raise TGIError("Unexpected TGI response format")
            
            if not generated_text:
                raise TGIError("Empty response from TGI server")
            
            logger.info(
                "TGI request successful",
                response_length=len(generated_text),
                prompt_length=len(prompt)
            )
            
            return generated_text.strip()
            
        except httpx.TimeoutException:
            error_msg = f"TGI request timed out after {self.timeout} seconds"
            logger.error("TGI timeout", timeout=self.timeout)
            raise TGIError(error_msg)
        
        except httpx.ConnectError:
            error_msg = f"Failed to connect to TGI server at {self.base_url}"
            logger.error("TGI connection error", base_url=self.base_url)
            raise TGIError(error_msg)
        
        except Exception as e:
            if isinstance(e, TGIError):
                raise
            
            error_msg = f"TGI request failed: {str(e)}"
            logger.error("TGI unexpected error", error=str(e))
            raise TGIError(error_msg)
    
    async def get_model_info(self) -> Dict[str, Any]:
        """Get model information from TGI server."""
        try:
            response = await self.client.get(f"{self.base_url}/info")
            response.raise_for_status()
            return await response.json()
        except Exception as e:
            logger.error("Failed to get TGI model info", error=str(e))
            raise TGIError(f"Failed to get model info: {str(e)}")


# Global TGI client instance
tgi_client = TGIClient()
