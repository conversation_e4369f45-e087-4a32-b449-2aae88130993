"""
Chat service for Bible Companion API.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

import structlog
from bson import ObjectId

from backend.core.auth import User
from backend.core.database import mongodb_client, neo4j_driver
from backend.core.exceptions import DatabaseError, NotFoundError, ValidationError
from backend.models.chat import (
    ChatMessage,
    ChatRequest,
    ChatResponse,
    ChatThread,
    ConversationBuffer,
    VerseContext
)
from backend.services.tgi_client import tgi_client

logger = structlog.get_logger()


class ChatService:
    """Service for managing chat conversations."""
    
    def __init__(self):
        self.mongodb = mongodb_client
        self.neo4j = neo4j_driver
        self.tgi = tgi_client
    
    async def create_chat_thread(self, user: User, title: Optional[str] = None) -> ChatThread:
        """Create a new chat thread."""
        try:
            thread = ChatThread(
                id=str(ObjectId()),
                user_id=user.uid,
                title=title or "New Chat",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Save to MongoDB
            collection = self.mongodb.get_collection("chat_threads")
            await collection.insert_one(thread.model_dump())
            
            logger.info("Chat thread created", thread_id=thread.id, user_id=user.uid)
            return thread
            
        except Exception as e:
            logger.error("Failed to create chat thread", error=str(e), user_id=user.uid)
            raise DatabaseError(f"Failed to create chat thread: {str(e)}")
    
    async def get_chat_thread(self, thread_id: str, user: User) -> ChatThread:
        """Get chat thread by ID."""
        try:
            collection = self.mongodb.get_collection("chat_threads")
            thread_data = await collection.find_one({
                "_id": ObjectId(thread_id),
                "user_id": user.uid
            })
            
            if not thread_data:
                raise NotFoundError(f"Chat thread {thread_id} not found")
            
            # Convert ObjectId to string
            thread_data["id"] = str(thread_data.pop("_id"))
            return ChatThread(**thread_data)
            
        except NotFoundError:
            raise
        except Exception as e:
            logger.error("Failed to get chat thread", error=str(e), thread_id=thread_id)
            raise DatabaseError(f"Failed to get chat thread: {str(e)}")
    
    async def get_conversation_buffer(self, thread_id: str) -> ConversationBuffer:
        """Get or create conversation buffer for thread."""
        try:
            collection = self.mongodb.get_collection("conversation_buffers")
            buffer_data = await collection.find_one({"thread_id": thread_id})
            
            if buffer_data:
                # Convert ObjectId and parse messages
                buffer_data.pop("_id", None)
                messages = [ChatMessage(**msg) for msg in buffer_data.get("messages", [])]
                buffer_data["messages"] = messages
                return ConversationBuffer(**buffer_data)
            else:
                # Create new buffer
                buffer = ConversationBuffer(thread_id=thread_id)
                await collection.insert_one(buffer.model_dump())
                return buffer
                
        except Exception as e:
            logger.error("Failed to get conversation buffer", error=str(e), thread_id=thread_id)
            raise DatabaseError(f"Failed to get conversation buffer: {str(e)}")
    
    async def save_conversation_buffer(self, buffer: ConversationBuffer) -> None:
        """Save conversation buffer to MongoDB."""
        try:
            collection = self.mongodb.get_collection("conversation_buffers")
            await collection.replace_one(
                {"thread_id": buffer.thread_id},
                buffer.model_dump(),
                upsert=True
            )
            
        except Exception as e:
            logger.error("Failed to save conversation buffer", error=str(e))
            raise DatabaseError(f"Failed to save conversation buffer: {str(e)}")
    
    async def get_verse_context(self, user: User, limit: int = 20) -> List[VerseContext]:
        """Get recent verses read by user from Neo4j."""
        try:
            query = """
            MATCH (u:User {authId: $user_id})-[r:READ]->(v:Verse)
            RETURN v.book as book, v.chapter as chapter, v.verse as verse, 
                   v.text as text, v.translation as translation, r.timestamp as read_at
            ORDER BY r.timestamp DESC
            LIMIT $limit
            """
            
            records = await self.neo4j.execute_query(
                query,
                {"user_id": user.uid, "limit": limit}
            )
            
            verses = []
            for record in records:
                verses.append(VerseContext(
                    book=record["book"],
                    chapter=record["chapter"],
                    verse=record["verse"],
                    text=record["text"],
                    translation=record.get("translation", "NIV"),
                    read_at=record["read_at"]
                ))
            
            logger.info("Retrieved verse context", user_id=user.uid, verse_count=len(verses))
            return verses
            
        except Exception as e:
            logger.error("Failed to get verse context", error=str(e), user_id=user.uid)
            # Return empty list if Neo4j fails (graceful degradation)
            return []
    
    def build_prompt_with_context(
        self,
        user_prompt: str,
        conversation_context: str,
        verse_context: List[VerseContext]
    ) -> str:
        """Build enhanced prompt with conversation and verse context."""
        
        # System prompt for Gemma
        system_prompt = """You are a helpful AI assistant for a Bible companion app. You provide thoughtful, biblically-informed responses to questions about faith, scripture, and Christian living. 

Guidelines:
- Be respectful and encouraging
- Reference relevant Bible verses when appropriate
- Provide practical, actionable advice
- Keep responses concise but meaningful
- If you're unsure about theological matters, acknowledge uncertainty

"""
        
        # Add verse context if available
        if verse_context:
            context_text = "Recent verses the user has been reading:\n"
            for verse in verse_context[:5]:  # Limit to 5 most recent
                context_text += f"- {verse.to_context_string()}\n"
            context_text += "\n"
        else:
            context_text = ""
        
        # Add conversation history
        if conversation_context:
            conversation_text = f"Previous conversation:\n{conversation_context}\n\n"
        else:
            conversation_text = ""
        
        # Combine all parts
        full_prompt = f"{system_prompt}{context_text}{conversation_text}Human: {user_prompt}\n\nAssistant:"
        
        return full_prompt
    
    async def process_chat_request(self, request: ChatRequest, user: User) -> ChatResponse:
        """Process chat request and generate response."""
        try:
            # Get or create thread
            if request.thread_id:
                thread = await self.get_chat_thread(request.thread_id, user)
            else:
                thread = await self.create_chat_thread(user)
            
            # Get conversation buffer
            buffer = await self.get_conversation_buffer(thread.id)
            
            # Get verse context if requested
            verse_context = []
            if request.include_context:
                verse_context = await self.get_verse_context(user)
            
            # Build enhanced prompt
            conversation_context = buffer.get_context()
            full_prompt = self.build_prompt_with_context(
                request.prompt,
                conversation_context,
                verse_context
            )
            
            # Generate response using TGI
            response_text = await self.tgi.generate_text(
                prompt=full_prompt,
                max_new_tokens=request.max_tokens or 512,
                temperature=request.temperature or 0.7,
                top_p=0.9,
                stop_sequences=["Human:", "Assistant:"]
            )
            
            # Create chat messages
            user_message = ChatMessage(
                id=str(ObjectId()),
                role="user",
                content=request.prompt,
                timestamp=datetime.utcnow()
            )
            
            assistant_message = ChatMessage(
                id=str(ObjectId()),
                role="assistant",
                content=response_text,
                timestamp=datetime.utcnow()
            )
            
            # Update conversation buffer
            buffer.add_message(user_message)
            buffer.add_message(assistant_message)
            await self.save_conversation_buffer(buffer)
            
            # Save messages to MongoDB
            messages_collection = self.mongodb.get_collection("messages")
            await messages_collection.insert_many([
                {**user_message.model_dump(), "thread_id": thread.id},
                {**assistant_message.model_dump(), "thread_id": thread.id}
            ])
            
            # Update thread timestamp
            threads_collection = self.mongodb.get_collection("chat_threads")
            await threads_collection.update_one(
                {"_id": ObjectId(thread.id)},
                {"$set": {"updated_at": datetime.utcnow()}}
            )
            
            # Create response
            response = ChatResponse(
                thread_id=thread.id,
                answer=response_text,
                cursor=assistant_message.id,
                context_verses=[verse.model_dump() for verse in verse_context[:5]],
                metadata={
                    "model": "gemma-2b-it",
                    "tokens_used": len(full_prompt.split()) + len(response_text.split()),
                    "context_verses_count": len(verse_context)
                }
            )
            
            logger.info(
                "Chat request processed",
                thread_id=thread.id,
                user_id=user.uid,
                response_length=len(response_text)
            )
            
            return response
            
        except Exception as e:
            logger.error("Failed to process chat request", error=str(e), user_id=user.uid)
            raise


# Global chat service instance
chat_service = ChatService()
