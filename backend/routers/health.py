"""
Health check router for Bible Companion API.
"""

from datetime import datetime
from typing import Dict, Any

import structlog
from fastapi import APIRouter

from backend.core.database import mongodb_client, neo4j_driver
from backend.services.tgi_client import tgi_client

logger = structlog.get_logger()

router = APIRouter()


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    
    Returns:
        Dict with service status and timestamp
    """
    return {
        "status": "healthy",
        "service": "Bible Companion API",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }


@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """
    Detailed health check with dependency status.
    
    Returns:
        Dict with detailed status of all services
    """
    health_status = {
        "status": "healthy",
        "service": "Bible Companion API",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "dependencies": {}
    }
    
    overall_healthy = True
    
    # Check MongoDB
    try:
        mongodb_healthy = await mongodb_client.health_check()
        health_status["dependencies"]["mongodb"] = {
            "status": "healthy" if mongodb_healthy else "unhealthy",
            "connected": mongodb_healthy
        }
        if not mongodb_healthy:
            overall_healthy = False
    except Exception as e:
        health_status["dependencies"]["mongodb"] = {
            "status": "unhealthy",
            "error": str(e),
            "connected": False
        }
        overall_healthy = False
    
    # Check Neo4j
    try:
        neo4j_healthy = await neo4j_driver.health_check()
        health_status["dependencies"]["neo4j"] = {
            "status": "healthy" if neo4j_healthy else "unhealthy",
            "connected": neo4j_healthy
        }
        if not neo4j_healthy:
            overall_healthy = False
    except Exception as e:
        health_status["dependencies"]["neo4j"] = {
            "status": "unhealthy",
            "error": str(e),
            "connected": False
        }
        overall_healthy = False
    
    # Check TGI
    try:
        tgi_healthy = await tgi_client.health_check()
        health_status["dependencies"]["tgi"] = {
            "status": "healthy" if tgi_healthy else "unhealthy",
            "connected": tgi_healthy
        }
        if not tgi_healthy:
            overall_healthy = False
    except Exception as e:
        health_status["dependencies"]["tgi"] = {
            "status": "unhealthy",
            "error": str(e),
            "connected": False
        }
        overall_healthy = False
    
    # Update overall status
    health_status["status"] = "healthy" if overall_healthy else "degraded"
    
    logger.info(
        "Health check performed",
        overall_status=health_status["status"],
        mongodb=health_status["dependencies"]["mongodb"]["status"],
        neo4j=health_status["dependencies"]["neo4j"]["status"],
        tgi=health_status["dependencies"]["tgi"]["status"]
    )
    
    return health_status


@router.get("/ready")
async def readiness_check() -> Dict[str, Any]:
    """
    Readiness check for Kubernetes/container orchestration.
    
    Returns:
        Dict indicating if service is ready to accept traffic
    """
    try:
        # Check critical dependencies
        mongodb_ready = await mongodb_client.health_check()
        
        if mongodb_ready:
            return {
                "status": "ready",
                "timestamp": datetime.utcnow().isoformat()
            }
        else:
            return {
                "status": "not_ready",
                "reason": "MongoDB not available",
                "timestamp": datetime.utcnow().isoformat()
            }
    
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {
            "status": "not_ready",
            "reason": f"Health check failed: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/live")
async def liveness_check() -> Dict[str, Any]:
    """
    Liveness check for Kubernetes/container orchestration.
    
    Returns:
        Dict indicating if service is alive
    """
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat()
    }
