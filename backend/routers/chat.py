"""
Chat router for Bible Companion API.
"""

import structlog
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Request
from slowapi import Limiter
from slowapi.util import get_remote_address

from backend.core.auth import User, get_current_user
from backend.core.config import get_settings
from backend.models.chat import ChatRe<PERSON>, ChatResponse
from backend.services.chat_service import chat_service

logger = structlog.get_logger()
settings = get_settings()

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

router = APIRouter()


@router.post("/chat", response_model=ChatResponse)
@limiter.limit(f"{settings.RATE_LIMIT_PER_MINUTE}/minute")
async def create_chat(
    request: Request,
    chat_request: ChatRequest,
    user: User = Depends(get_current_user)
) -> ChatResponse:
    """
    Create a chat message and get AI response.
    
    This endpoint processes a user's chat message, optionally including
    context from recently read Bible verses, and returns an AI-generated
    response using the Gemma model.
    
    **Rate Limit**: 60 requests per minute per user
    
    **Authentication**: Requires valid Firebase JWT token
    
    Args:
        request: FastAPI request object (for rate limiting)
        chat_request: Chat request containing prompt and options
        user: Authenticated user from JWT token
        
    Returns:
        ChatResponse: AI response with thread ID and context
        
    Raises:
        400: Invalid request parameters
        401: Authentication required
        429: Rate limit exceeded
        500: Internal server error
        502: TGI service unavailable
    """
    logger.info(
        "Chat request received",
        user_id=user.uid,
        thread_id=chat_request.thread_id,
        prompt_length=len(chat_request.prompt),
        include_context=chat_request.include_context
    )
    
    try:
        response = await chat_service.process_chat_request(chat_request, user)
        
        logger.info(
            "Chat response generated",
            user_id=user.uid,
            thread_id=response.thread_id,
            response_length=len(response.answer),
            context_verses_count=len(response.context_verses)
        )
        
        return response
        
    except Exception as e:
        logger.error(
            "Chat request failed",
            user_id=user.uid,
            error=str(e),
            thread_id=chat_request.thread_id
        )
        raise


@router.get("/chat/threads")
@limiter.limit("30/minute")
async def get_user_threads(
    request: Request,
    user: User = Depends(get_current_user),
    limit: int = 20,
    offset: int = 0
):
    """
    Get user's chat threads.
    
    **Rate Limit**: 30 requests per minute per user
    
    Args:
        request: FastAPI request object
        user: Authenticated user
        limit: Maximum number of threads to return (default: 20, max: 100)
        offset: Number of threads to skip (default: 0)
        
    Returns:
        List of chat threads for the user
    """
    # Validate parameters
    limit = min(limit, 100)  # Cap at 100
    offset = max(offset, 0)  # Ensure non-negative
    
    try:
        from backend.core.database import mongodb_client
        from bson import ObjectId
        
        collection = mongodb_client.get_collection("chat_threads")
        
        # Get threads for user
        cursor = collection.find(
            {"user_id": user.uid, "is_active": True}
        ).sort("updated_at", -1).skip(offset).limit(limit)
        
        threads = []
        async for thread_data in cursor:
            thread_data["id"] = str(thread_data.pop("_id"))
            threads.append(thread_data)
        
        logger.info(
            "User threads retrieved",
            user_id=user.uid,
            thread_count=len(threads),
            limit=limit,
            offset=offset
        )
        
        return {
            "threads": threads,
            "limit": limit,
            "offset": offset,
            "has_more": len(threads) == limit
        }
        
    except Exception as e:
        logger.error("Failed to get user threads", user_id=user.uid, error=str(e))
        raise


@router.get("/chat/threads/{thread_id}/messages")
@limiter.limit("60/minute")
async def get_thread_messages(
    request: Request,
    thread_id: str,
    user: User = Depends(get_current_user),
    limit: int = 50,
    cursor: str = None
):
    """
    Get messages from a chat thread.
    
    **Rate Limit**: 60 requests per minute per user
    
    Args:
        request: FastAPI request object
        thread_id: Chat thread ID
        user: Authenticated user
        limit: Maximum number of messages to return (default: 50, max: 100)
        cursor: Pagination cursor (message ID)
        
    Returns:
        List of messages in the thread
    """
    # Validate thread ownership
    thread = await chat_service.get_chat_thread(thread_id, user)
    
    # Validate parameters
    limit = min(limit, 100)  # Cap at 100
    
    try:
        from backend.core.database import mongodb_client
        from bson import ObjectId
        
        collection = mongodb_client.get_collection("messages")
        
        # Build query
        query = {"thread_id": thread_id}
        if cursor:
            query["_id"] = {"$lt": ObjectId(cursor)}
        
        # Get messages
        cursor_obj = collection.find(query).sort("timestamp", -1).limit(limit)
        
        messages = []
        async for message_data in cursor_obj:
            message_data["id"] = str(message_data.pop("_id"))
            messages.append(message_data)
        
        # Reverse to get chronological order
        messages.reverse()
        
        logger.info(
            "Thread messages retrieved",
            user_id=user.uid,
            thread_id=thread_id,
            message_count=len(messages)
        )
        
        return {
            "messages": messages,
            "thread_id": thread_id,
            "limit": limit,
            "has_more": len(messages) == limit,
            "next_cursor": messages[-1]["id"] if messages else None
        }
        
    except Exception as e:
        logger.error(
            "Failed to get thread messages",
            user_id=user.uid,
            thread_id=thread_id,
            error=str(e)
        )
        raise


@router.delete("/chat/threads/{thread_id}")
@limiter.limit("10/minute")
async def delete_thread(
    request: Request,
    thread_id: str,
    user: User = Depends(get_current_user)
):
    """
    Delete a chat thread (soft delete).
    
    **Rate Limit**: 10 requests per minute per user
    
    Args:
        request: FastAPI request object
        thread_id: Chat thread ID to delete
        user: Authenticated user
        
    Returns:
        Success confirmation
    """
    # Validate thread ownership
    thread = await chat_service.get_chat_thread(thread_id, user)
    
    try:
        from backend.core.database import mongodb_client
        from bson import ObjectId
        from datetime import datetime
        
        collection = mongodb_client.get_collection("chat_threads")
        
        # Soft delete (mark as inactive)
        await collection.update_one(
            {"_id": ObjectId(thread_id)},
            {
                "$set": {
                    "is_active": False,
                    "deleted_at": datetime.utcnow()
                }
            }
        )
        
        logger.info("Chat thread deleted", user_id=user.uid, thread_id=thread_id)
        
        return {"message": "Thread deleted successfully", "thread_id": thread_id}
        
    except Exception as e:
        logger.error(
            "Failed to delete thread",
            user_id=user.uid,
            thread_id=thread_id,
            error=str(e)
        )
        raise
