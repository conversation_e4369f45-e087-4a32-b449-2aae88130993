"""
Bible Companion FastAPI Backend
"""

import os
import logging
from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

from database import connect_to_mongo, close_mongo_connection
from auth import initialize_firebase
from routers import circles

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO if os.getenv("DEBUG", "False").lower() == "true" else logging.WARNING,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    logger.info("Starting Bible Companion API...")

    try:
        # Initialize Firebase
        initialize_firebase()

        # Connect to MongoDB
        await connect_to_mongo()

        # Run Neo4j migrations if enabled
        from run_neo4j_migrations import should_migrate_on_startup, Neo4jMigrationRunner
        if should_migrate_on_startup():
            logger.info("Running Neo4j migrations on startup...")
            try:
                runner = Neo4jMigrationRunner()
                success = runner.run_migrations()
                if success:
                    logger.info("Neo4j migrations completed successfully")
                else:
                    logger.error("Neo4j migrations failed")
                    # Don't fail startup for migration issues in development
                    if os.getenv("DEBUG", "False").lower() != "true":
                        raise Exception("Neo4j migrations failed")
            except Exception as e:
                logger.error(f"Neo4j migration error: {e}")
                # Don't fail startup for migration issues in development
                if os.getenv("DEBUG", "False").lower() != "true":
                    raise

        logger.info("Bible Companion API started successfully")

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        raise

    yield

    # Shutdown
    logger.info("Shutting down Bible Companion API...")
    await close_mongo_connection()
    logger.info("Bible Companion API shut down successfully")

# Create FastAPI application
app = FastAPI(
    title="Bible Companion API",
    description="REST API for the Bible Companion mobile application",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Configure CORS
allowed_origins = os.getenv("ALLOWED_ORIGINS", "").split(",")
if not allowed_origins or allowed_origins == [""]:
    allowed_origins = ["*"]  # Allow all origins in development

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(circles.router)

@app.get("/", tags=["Health"])
async def root():
    """Root endpoint - API health check"""
    return {
        "message": "Bible Companion API",
        "version": "1.0.0",
        "status": "healthy"
    }

@app.get("/health", tags=["Health"])
async def health_check():
    """Detailed health check endpoint"""
    try:
        from database import mongodb
        
        # Check database connection
        if mongodb.database:
            await mongodb.database.command("ping")
            db_status = "connected"
        else:
            db_status = "disconnected"
            
        return {
            "status": "healthy",
            "database": db_status,
            "version": "1.0.0"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unavailable")

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    debug = os.getenv("DEBUG", "False").lower() == "true"
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info" if debug else "warning"
    )
