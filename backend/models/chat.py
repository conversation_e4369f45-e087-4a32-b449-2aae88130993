"""
Chat-related Pydantic models for Bible Companion API.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ChatMessage(BaseModel):
    """Individual chat message model."""
    
    id: Optional[str] = Field(None, description="Message ID")
    role: str = Field(..., description="Message role (user/assistant)")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Message timestamp")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class ChatThread(BaseModel):
    """Chat thread model."""
    
    id: Optional[str] = Field(None, description="Thread ID")
    user_id: str = Field(..., description="User ID")
    title: Optional[str] = Field(None, description="Thread title")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    is_active: bool = Field(default=True, description="Thread active status")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")


class ChatRequest(BaseModel):
    """Chat request model."""
    
    thread_id: Optional[str] = Field(None, description="Thread ID (optional for new threads)")
    prompt: str = Field(..., min_length=1, max_length=2000, description="User prompt")
    include_context: bool = Field(default=True, description="Include verse context")
    max_tokens: Optional[int] = Field(default=512, ge=1, le=2048, description="Maximum response tokens")
    temperature: Optional[float] = Field(default=0.7, ge=0.0, le=2.0, description="Response temperature")


class ChatResponse(BaseModel):
    """Chat response model."""
    
    thread_id: str = Field(..., description="Thread ID")
    answer: str = Field(..., description="AI assistant response")
    cursor: str = Field(..., description="Conversation cursor for pagination")
    context_verses: List[Dict[str, Any]] = Field(default_factory=list, description="Context verses used")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Response metadata")


class TGIRequest(BaseModel):
    """TGI (Text Generation Inference) request model."""
    
    inputs: str = Field(..., description="Input text for generation")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Generation parameters")
    stream: bool = Field(default=False, description="Stream response")


class TGIResponse(BaseModel):
    """TGI response model."""
    
    generated_text: str = Field(..., description="Generated text")
    details: Optional[Dict[str, Any]] = Field(None, description="Generation details")


class ConversationBuffer(BaseModel):
    """LangChain conversation buffer model."""
    
    thread_id: str = Field(..., description="Thread ID")
    messages: List[ChatMessage] = Field(default_factory=list, description="Conversation messages")
    max_length: int = Field(default=10, description="Maximum messages to keep")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    
    def add_message(self, message: ChatMessage) -> None:
        """Add message to buffer and maintain max length."""
        self.messages.append(message)
        self.updated_at = datetime.utcnow()
        
        # Keep only the most recent messages
        if len(self.messages) > self.max_length:
            self.messages = self.messages[-self.max_length:]
    
    def get_context(self) -> str:
        """Get conversation context as formatted string."""
        context_parts = []
        for msg in self.messages:
            role = "Human" if msg.role == "user" else "Assistant"
            context_parts.append(f"{role}: {msg.content}")
        
        return "\n".join(context_parts)


class VerseContext(BaseModel):
    """Bible verse context model."""
    
    book: str = Field(..., description="Bible book name")
    chapter: int = Field(..., description="Chapter number")
    verse: int = Field(..., description="Verse number")
    text: str = Field(..., description="Verse text")
    translation: str = Field(default="NIV", description="Bible translation")
    read_at: datetime = Field(..., description="When user read this verse")
    
    def to_context_string(self) -> str:
        """Convert verse to context string."""
        return f"{self.book} {self.chapter}:{self.verse} ({self.translation}) - {self.text}"
