# Bible Companion Backend API

FastAPI backend for the Bible Companion mobile application, providing REST APIs for prayer circles and other features.

## Features

- **Prayer Circles API**: Create, join, and manage prayer circles
- **Firebase Authentication**: JWT token verification
- **MongoDB Integration**: Async database operations with Motor
- **QR Code Generation**: For easy circle joining
- **OpenAPI Documentation**: Auto-generated Swagger docs
- **Comprehensive Testing**: Unit tests with pytest

## Quick Start

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Environment Setup

Copy the example environment file and configure:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:
- MongoDB connection string
- Firebase service account credentials
- API settings

### 3. Run the Server

```bash
# Development mode with auto-reload
python main.py

# Or using uvicorn directly
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 4. Access Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health

## API Endpoints

### Prayer Circles (`/v1/circles`)

| Method | Path | Description |
|--------|------|-------------|
| `POST` | `/` | Create new prayer circle |
| `GET` | `/mine` | Get user's circles |
| `POST` | `/{circleId}/join` | Join a circle |
| `GET` | `/{circleId}` | Get circle details |

### Authentication

All endpoints require a Firebase JWT token in the Authorization header:

```
Authorization: Bearer <firebase-jwt-token>
```

## Testing

Run the test suite:

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test file
pytest tests/test_circles.py
```

## Project Structure

```
backend/
├── main.py              # FastAPI application entry point
├── database.py          # MongoDB connection and helpers
├── auth.py              # Firebase authentication
├── routers/
│   ├── __init__.py
│   └── circles.py       # Prayer circles endpoints
├── schemas/
│   ├── __init__.py
│   └── circles.py       # Pydantic models
├── tests/
│   ├── __init__.py
│   └── test_circles.py  # Unit tests
├── requirements.txt     # Python dependencies
├── .env.example        # Environment variables template
└── README.md           # This file
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017` |
| `MONGODB_DB_NAME` | Database name | `bible_companion` |
| `FIREBASE_PROJECT_ID` | Firebase project ID | Required |
| `FIREBASE_PRIVATE_KEY` | Firebase private key | Required |
| `FIREBASE_CLIENT_EMAIL` | Firebase client email | Required |
| `API_HOST` | Server host | `0.0.0.0` |
| `API_PORT` | Server port | `8000` |
| `DEBUG` | Debug mode | `True` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `*` |

### Firebase Setup

1. Go to Firebase Console → Project Settings → Service Accounts
2. Generate a new private key
3. Extract the credentials and add to your `.env` file

### MongoDB Setup

The API supports both local MongoDB and MongoDB Atlas:

```bash
# Local MongoDB
MONGODB_URI=mongodb://localhost:27017

# MongoDB Atlas
MONGODB_URI=mongodb+srv://username:<EMAIL>/
```

## Development

### Code Style

The project follows Python best practices:

- **Black** for code formatting
- **Ruff** for linting
- **Type hints** for better code quality
- **Async/await** for database operations

### Adding New Endpoints

1. Create Pydantic models in `schemas/`
2. Add router in `routers/`
3. Include router in `main.py`
4. Write tests in `tests/`

### Database Indexes

The application automatically creates these indexes on startup:

- `users.authId` (unique)
- `users.profile.email`
- `users.profile.phoneNumber`
- `circles.ownerId`
- `circles.memberIds`
- `circles.ownerId + circles.name` (unique compound)

## Deployment

### Docker (Recommended)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Production Considerations

- Use a production WSGI server (Gunicorn + Uvicorn)
- Set up proper logging and monitoring
- Configure environment-specific settings
- Use secrets management for sensitive data
- Set up health checks and graceful shutdowns

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure all tests pass before submitting

## License

This project is part of the Bible Companion application.
