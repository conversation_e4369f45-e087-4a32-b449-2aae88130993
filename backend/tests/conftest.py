"""
Pytest configuration and fixtures for Bible Companion API tests.
"""

import asyncio
import os
from typing import As<PERSON><PERSON>enerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
import pytest_asyncio
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Set test environment
os.environ["DEBUG"] = "true"
os.environ["SECRET_KEY"] = "test-secret-key"
os.environ["MONGODB_URI"] = "mongodb://localhost:27017"
os.environ["MONGODB_DB_NAME"] = "bible_companion_test"
os.environ["NEO4J_URI"] = "bolt://localhost:7687"
os.environ["NEO4J_USERNAME"] = "neo4j"
os.environ["NEO4J_PASSWORD"] = "test-password"
os.environ["TGI_BASE_URL"] = "http://localhost:8080"
os.environ["FIREBASE_PROJECT_ID"] = "test-project"
os.environ["FIREBASE_CREDENTIALS_PATH"] = ""


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture
async def app():
    """Create FastAPI app instance for testing."""
    from backend.main import create_app
    
    app = create_app()
    return app


@pytest_asyncio.fixture
async def client(app) -> AsyncGenerator[AsyncClient, None]:
    """Create async HTTP client for testing."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def sync_client(app) -> TestClient:
    """Create sync HTTP client for testing."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    from backend.core.auth import User
    
    return User(
        uid="test-user-123",
        email="<EMAIL>",
        email_verified=True,
        custom_claims={}
    )


@pytest.fixture
def mock_admin_user():
    """Mock admin user."""
    from backend.core.auth import User
    
    return User(
        uid="admin-user-123",
        email="<EMAIL>",
        email_verified=True,
        custom_claims={"admin": True}
    )


@pytest.fixture
def mock_firebase_token():
    """Mock Firebase JWT token."""
    return "mock-firebase-token-12345"


@pytest_asyncio.fixture
async def mock_mongodb():
    """Mock MongoDB client."""
    mock_client = AsyncMock()
    mock_db = AsyncMock()
    mock_collection = AsyncMock()
    
    mock_client.get_collection.return_value = mock_collection
    mock_client.health_check.return_value = True
    
    return mock_client


@pytest_asyncio.fixture
async def mock_neo4j():
    """Mock Neo4j client."""
    mock_driver = AsyncMock()
    mock_driver.health_check.return_value = True
    mock_driver.execute_query.return_value = []
    
    return mock_driver


@pytest_asyncio.fixture
async def mock_tgi_client():
    """Mock TGI client."""
    mock_client = AsyncMock()
    mock_client.health_check.return_value = True
    mock_client.generate_text.return_value = "This is a test response from the AI assistant."
    
    return mock_client


@pytest.fixture
def mock_chat_request():
    """Mock chat request."""
    from backend.models.chat import ChatRequest
    
    return ChatRequest(
        thread_id=None,
        prompt="What is the meaning of faith?",
        include_context=True,
        max_tokens=512,
        temperature=0.7
    )


@pytest.fixture
def mock_chat_thread():
    """Mock chat thread."""
    from backend.models.chat import ChatThread
    from datetime import datetime
    
    return ChatThread(
        id="507f1f77bcf86cd799439011",  # Valid ObjectId format
        user_id="test-user-123",
        title="Test Chat",
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
        is_active=True
    )


@pytest.fixture
def mock_verse_context():
    """Mock verse context."""
    from backend.models.chat import VerseContext
    from datetime import datetime
    
    return [
        VerseContext(
            book="John",
            chapter=3,
            verse=16,
            text="For God so loved the world that he gave his one and only Son...",
            translation="NIV",
            read_at=datetime.utcnow()
        ),
        VerseContext(
            book="Romans",
            chapter=8,
            verse=28,
            text="And we know that in all things God works for the good...",
            translation="NIV",
            read_at=datetime.utcnow()
        )
    ]
