{"tests/test_chat_api.py::TestChatAPI::test_create_chat_success": true, "tests/test_chat_api.py::TestChatAPI::test_get_user_threads": true, "tests/test_chat_api.py::TestChatAPI::test_get_thread_messages": true, "tests/test_chat_api.py::TestChatAPI::test_delete_thread": true, "tests/test_tgi_client.py::TestTGIClient::test_generate_text_success": true, "tests/test_tgi_client.py::TestTGIClient::test_generate_text_list_response": true, "tests/test_tgi_client.py::TestTGIClient::test_generate_text_http_error": true, "tests/test_tgi_client.py::TestTGIClient::test_generate_text_timeout": true, "tests/test_tgi_client.py::TestTGIClient::test_generate_text_connection_error": true, "tests/test_tgi_client.py::TestTGIClient::test_generate_text_empty_response": true, "tests/test_tgi_client.py::TestTGIClient::test_get_model_info_success": true}