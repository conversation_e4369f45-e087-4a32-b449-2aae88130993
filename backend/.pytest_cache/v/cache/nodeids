["tests/test_chat_api.py::TestChatAPI::test_create_chat_invalid_token", "tests/test_chat_api.py::TestChatAPI::test_create_chat_success", "tests/test_chat_api.py::TestChatAPI::test_create_chat_tgi_error", "tests/test_chat_api.py::TestChatAPI::test_create_chat_unauthorized", "tests/test_chat_api.py::TestChatAPI::test_delete_thread", "tests/test_chat_api.py::TestChatAPI::test_get_thread_messages", "tests/test_chat_api.py::TestChatAPI::test_get_user_threads", "tests/test_tgi_client.py::TestTGIClient::test_generate_text_connection_error", "tests/test_tgi_client.py::TestTGIClient::test_generate_text_empty_response", "tests/test_tgi_client.py::TestTGIClient::test_generate_text_http_error", "tests/test_tgi_client.py::TestTGIClient::test_generate_text_list_response", "tests/test_tgi_client.py::TestTGIClient::test_generate_text_success", "tests/test_tgi_client.py::TestTGIClient::test_generate_text_timeout", "tests/test_tgi_client.py::TestTGIClient::test_get_model_info_error", "tests/test_tgi_client.py::TestTGIClient::test_get_model_info_success", "tests/test_tgi_client.py::TestTGIClient::test_health_check_failure", "tests/test_tgi_client.py::TestTGIClient::test_health_check_success"]