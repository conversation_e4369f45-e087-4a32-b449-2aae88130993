# 🏗️ Infrastructure Setup Guide

This guide walks you through setting up the production infrastructure for the Bible Companion app using Pulumi.

## 📋 Overview

The infrastructure includes:
- **MongoDB Atlas M10 cluster** (AWS eu-central-1)
- **Cloudflare R2 bucket** for verse media storage
- **GitHub Secrets** for CI/CD integration

## 🚀 Quick Start

### 1. Prerequisites

```bash
# Install Pulumi CLI
curl -fsSL https://get.pulumi.com | sh

# Navigate to infrastructure directory
cd infrastructure

# Install Python dependencies
pip install -r requirements.txt
```

### 2. Automated Setup

Run the setup script for guided configuration:

```bash
./setup.sh
```

This will prompt you for all required API keys and configuration.

### 3. Manual Setup (Alternative)

If you prefer manual configuration:

```bash
# Initialize Pulumi stack
pulumi stack init prod

# MongoDB Atlas (get from https://cloud.mongodb.com/v2#/org/YOUR_ORG_ID/access/apiKeys)
pulumi config set mongodbatlas:publicKey <your-atlas-public-key>
pulumi config set mongodbatlas:privateKey <your-atlas-private-key> --secret
pulumi config set mongodbatlas:orgId <your-org-id>

# Database password (optional - will auto-generate if not provided)
pulumi config set bible-companion:dbPassword <strong-password> --secret

# Cloudflare (get from https://dash.cloudflare.com/)
pulumi config set cloudflare:accountId <your-cloudflare-account-id>
pulumi config set cloudflare:apiToken <your-cloudflare-api-token> --secret

# GitHub (get from https://github.com/settings/tokens)
pulumi config set github:token <your-github-token> --secret
pulumi config set bible-companion:githubRepo "Jpkay/bible"
```

### 4. Deploy Infrastructure

```bash
pulumi up
```

Review the planned changes and confirm deployment.

## 📊 API Keys Required

### MongoDB Atlas API Keys

1. Go to [MongoDB Atlas Console](https://cloud.mongodb.com/)
2. Navigate to **Organization** → **Access Manager** → **API Keys**
3. Create new API key with **Organization Project Creator** permissions
4. Note down the **Public Key** and **Private Key**
5. Get your **Organization ID** from the URL: `https://cloud.mongodb.com/v2#/org/{ORG_ID}/projects`

### Cloudflare API Token

1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/profile/api-tokens)
2. Click **Create Token**
3. Use **Custom token** template
4. Set permissions:
   - **Account** - `Cloudflare R2:Edit`
   - **Zone** - `Zone:Read` (if needed)
5. Set **Account Resources** to your account
6. Note down your **Account ID** from the dashboard

### GitHub Personal Access Token

1. Go to [GitHub Settings](https://github.com/settings/tokens)
2. Click **Generate new token (classic)**
3. Select scopes:
   - `repo` (Full control of private repositories)
   - `admin:repo_hook` (Full control of repository hooks)
4. Generate and copy the token

## 🔧 Post-Deployment

### 1. Update Local Environment

After successful deployment, update your local `.env` file:

```bash
# Get the MongoDB connection string
cd infrastructure
MONGO_URI=$(pulumi stack output cluster_connection_string)

# Update your .env file
cd ..
echo "EXPO_PUBLIC_MONGODB_URI=$MONGO_URI" >> .env
```

### 2. Configure R2 Access

The R2 bucket is created, but you need to set up access keys:

1. Go to [Cloudflare R2 Console](https://dash.cloudflare.com/)
2. Navigate to **R2 Object Storage** → **Manage R2 API tokens**
3. Create new token with **Object Read & Write** permissions
4. Update GitHub secrets manually:
   - Go to your repository → **Settings** → **Secrets and variables** → **Actions**
   - Update `R2_ACCESS_KEY` with your access key ID
   - Update `R2_SECRET_KEY` with your secret access key

### 3. Security Hardening

**⚠️ Important**: The initial setup allows all IPs (`0.0.0.0/0`) for development.

For production:
1. Go to [MongoDB Atlas Console](https://cloud.mongodb.com/)
2. Navigate to **Network Access**
3. Replace `0.0.0.0/0` with specific IP ranges
4. Consider using VPC peering for enhanced security

## 📈 Monitoring & Management

### View Infrastructure Status

```bash
cd infrastructure

# View all outputs
pulumi stack output

# View specific output
pulumi stack output cluster_connection_string

# View configuration
pulumi config

# View resource list
pulumi stack --show-urns
```

### Update Infrastructure

```bash
# Modify configuration
pulumi config set <key> <value>

# Apply changes
pulumi up

# View deployment history
pulumi stack history
```

### Cleanup (if needed)

```bash
# Destroy all resources
pulumi destroy

# Remove stack
pulumi stack rm prod
```

## 💰 Cost Estimation

- **MongoDB Atlas M10**: ~$57/month
- **Cloudflare R2**: Pay-per-use (~$0.015/GB/month)
- **GitHub**: Free for public repositories

Total estimated monthly cost: ~$60-70/month

## 🔍 Troubleshooting

### Common Issues

1. **Organization ID Error**
   ```
   Error: organization not found
   ```
   Solution: Get the correct Organization ID from the Atlas URL

2. **Cloudflare Token Permissions**
   ```
   Error: insufficient permissions
   ```
   Solution: Ensure token has `Account:Cloudflare R2:Edit` permission

3. **GitHub Repository Access**
   ```
   Error: repository not found
   ```
   Solution: Verify repository name format and token permissions

### Getting Help

1. Check the [Pulumi documentation](https://www.pulumi.com/docs/)
2. Review provider-specific docs:
   - [MongoDB Atlas Provider](https://www.pulumi.com/registry/packages/mongodbatlas/)
   - [Cloudflare Provider](https://www.pulumi.com/registry/packages/cloudflare/)
   - [GitHub Provider](https://www.pulumi.com/registry/packages/github/)

## 🔗 Integration with App

After infrastructure setup, your app will have:

1. **MongoDB connection** via `EXPO_PUBLIC_MONGODB_URI` environment variable
2. **R2 storage access** via GitHub secrets in CI/CD
3. **Automated deployments** with proper credentials

The app is already configured to use these environment variables in:
- `src/config/firebase.ts` (for environment variables)
- `src/services/mongodb.ts` (for database connection)

## 🎯 Next Steps

1. **Deploy infrastructure** using this guide
2. **Update local environment** with connection strings
3. **Test the connection** by running the app locally
4. **Set up CI/CD** using the GitHub secrets
5. **Monitor resources** in respective dashboards

For development workflow, see the main [README.md](./README.md) and [FIREBASE_MONGODB_SETUP.md](./FIREBASE_MONGODB_SETUP.md).
