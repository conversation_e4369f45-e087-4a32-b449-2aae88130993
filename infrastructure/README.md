# Bible Companion Infrastructure

This Pulumi program provisions the core infrastructure for the Bible Companion app:

- **MongoDB Atlas M10 cluster** in AWS eu-central-1
- **Cloudflare R2 bucket** for verse media storage  
- **GitHub Secrets** for CI/CD integration

## Prerequisites

1. **Install Pulumi CLI**
   ```bash
   curl -fsSL https://get.pulumi.com | sh
   ```

2. **Install Python dependencies**
   ```bash
   cd infrastructure
   pip install -r requirements.txt
   ```

3. **Get API Keys**
   - MongoDB Atlas: [API Keys](https://cloud.mongodb.com/v2#/org/YOUR_ORG_ID/access/apiKeys)
   - Cloudflare: [API Tokens](https://dash.cloudflare.com/profile/api-tokens)
   - GitHub: [Personal Access Tokens](https://github.com/settings/tokens)

## Setup

### 1. Initialize Pulumi Stack

```bash
cd infrastructure
pulumi stack init prod
```

### 2. Configure MongoDB Atlas

```bash
# Get these from MongoDB Atlas Console > Organization > Access Manager > API Keys
pulumi config set mongodbatlas:publicKey <your-atlas-public-key>
pulumi config set mongodbatlas:privateKey <your-atlas-private-key> --secret

# Get organization ID from Atlas URL: https://cloud.mongodb.com/v2#/org/{ORG_ID}/projects
pulumi config set mongodbatlas:orgId <your-org-id>

# Set database password (or let it auto-generate)
pulumi config set bible-companion:dbPassword <strong-password> --secret
```

### 3. Configure Cloudflare

```bash
# Get account ID from Cloudflare dashboard
pulumi config set cloudflare:accountId <your-cloudflare-account-id>

# Create API token with R2:Edit permissions
pulumi config set cloudflare:apiToken <your-cloudflare-api-token> --secret
```

### 4. Configure GitHub

```bash
# Create token with 'repo' and 'admin:repo_hook' scopes
pulumi config set github:token <your-github-token> --secret

# Optional: Set repository (defaults to Jpkay/bible)
pulumi config set bible-companion:githubRepo <owner/repo>
```

## Deployment

### Deploy Infrastructure

```bash
pulumi up
```

This will:
1. Create MongoDB Atlas project and M10 cluster
2. Set up database user with readWriteAnyDatabase permissions
3. Create Cloudflare R2 bucket for media storage
4. Store connection strings in GitHub Secrets

### View Outputs

```bash
pulumi stack output
```

Key outputs:
- `cluster_connection_string`: MongoDB connection string
- `r2_bucket_url`: R2 bucket URL
- `setup_instructions`: Next steps

## Post-Deployment

### 1. Update Local Environment

Copy the MongoDB connection string to your local `.env`:

```bash
# Get the connection string
MONGO_URI=$(pulumi stack output cluster_connection_string)

# Update your .env file
echo "EXPO_PUBLIC_MONGODB_URI=$MONGO_URI" >> ../.env
```

### 2. Configure R2 Access

Create R2 API tokens in Cloudflare dashboard and update GitHub secrets:

1. Go to Cloudflare Dashboard > R2 Object Storage > Manage R2 API tokens
2. Create token with R2:Edit permissions
3. Update GitHub secrets manually:
   - `R2_ACCESS_KEY`: Your R2 access key ID
   - `R2_SECRET_KEY`: Your R2 secret access key

### 3. Security Hardening

**Important**: The initial setup uses `0.0.0.0/0` for IP whitelisting. Update this:

1. Go to MongoDB Atlas Console
2. Navigate to Network Access
3. Replace `0.0.0.0/0` with specific IP ranges
4. Consider using VPC peering for production

### 4. Verify GitHub Secrets

Check that these secrets were created in your repository:
- `MONGO_URI`
- `R2_ACCESS_KEY` (needs manual update)
- `R2_SECRET_KEY` (needs manual update)

## Management

### View Resources

```bash
pulumi stack ls                    # List stacks
pulumi stack output               # View outputs
pulumi stack export              # Export stack state
```

### Update Configuration

```bash
pulumi config set <key> <value>   # Update config
pulumi up                        # Apply changes
```

### Cleanup

```bash
pulumi destroy                   # Remove all resources
pulumi stack rm prod            # Remove stack
```

## Troubleshooting

### Common Issues

1. **MongoDB Atlas Organization ID**
   ```bash
   # Get from Atlas URL: https://cloud.mongodb.com/v2#/org/{ORG_ID}/projects
   pulumi config set mongodbatlas:orgId <your-org-id>
   ```

2. **Cloudflare API Token Permissions**
   - Ensure token has `Account:Cloudflare R2:Edit` permission
   - Include correct account ID

3. **GitHub Repository Access**
   - Verify repository name format: `owner/repo`
   - Ensure token has `repo` and `admin:repo_hook` scopes

### Logs and Debugging

```bash
pulumi logs                      # View deployment logs
pulumi stack export --file state.json  # Export state for debugging
```

## Security Best Practices

1. **Rotate API Keys** regularly
2. **Use specific IP ranges** instead of 0.0.0.0/0
3. **Enable MongoDB Atlas** network security features
4. **Monitor resource usage** and costs
5. **Use least-privilege** access for API tokens

## Cost Optimization

- **MongoDB Atlas M10**: ~$57/month
- **Cloudflare R2**: Pay-per-use (very low cost)
- **GitHub**: Free for public repos

Monitor usage in respective dashboards to optimize costs.
