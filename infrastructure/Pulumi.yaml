name: bible-companion-infrastructure
runtime: python
description: Core infrastructure for Bible Companion app - MongoDB Atlas, Cloudflare R2, GitHub Secrets

config:
  # MongoDB Atlas configuration
  mongodbatlas:publicKey:
    description: MongoDB Atlas public API key
    type: string
  mongodbatlas:privateKey:
    description: MongoDB Atlas private API key (secret)
    type: string
    secret: true
  mongodbatlas:orgId:
    description: MongoDB Atlas organization ID
    type: string
  
  # Database configuration
  bible-companion:dbPassword:
    description: Database password for bc_app user (will generate if not provided)
    type: string
    secret: true
    default: ""
  
  # Cloudflare configuration
  cloudflare:accountId:
    description: Cloudflare account ID
    type: string
  cloudflare:apiToken:
    description: Cloudflare API token with R2 permissions
    type: string
    secret: true
  
  # GitHub configuration
  github:token:
    description: GitHub personal access token with repo and secrets permissions
    type: string
    secret: true
  
  # Repository configuration
  bible-companion:githubRepo:
    description: GitHub repository in format 'owner/repo'
    type: string
    default: "Jpkay/bible"

template:
  description: A Pulumi template for Bible Companion infrastructure
  config:
    mongodbatlas:publicKey:
      description: Your MongoDB Atlas public API key
    mongodbatlas:privateKey:
      description: Your MongoDB Atlas private API key
      secret: true
    mongodbatlas:orgId:
      description: Your MongoDB Atlas organization ID
    cloudflare:accountId:
      description: Your Cloudflare account ID
    cloudflare:apiToken:
      description: Your Cloudflare API token
      secret: true
    github:token:
      description: Your GitHub personal access token
      secret: true
